
from dotenv import load_dotenv
import os

load_dotenv()

from flask import Flask, render_template
from flask_login import LoginManager, login_required
from flask_socketio import SocketIO
from mongoengine import connect, disconnect
from models.user import User
from database import init_db
# Import view blueprints
from blueprints.auth import auth_bp
from blueprints.chat import chat_views
from blueprints.live import live_views
from blueprints.admin import admin_bp
from blueprints.profile_page import profile_page_bp
from spotify.routes import spotify_bp

# Import API blueprints
from api.chat import chat_api
from api.live import live_api
from api.friends import friends_api
from api.profile import profile_api
from api.admin import admin_api
from api.spotify import spotify_api
from api.auth import auth_api
from api.user import user_api
from api.admin.statistics import admin_statistics
from api.admin.model_usage_stats import model_usage_stats
from api.statistics.engagement import engagement_api
from api.upload import upload_api
from api.friends_theme import friends_theme_bp
# from api.activity import activity_api  # Removed - replaced with presence system
from api.presence import presence_api
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def create_app(use_socketio=False):
    app = Flask(__name__)
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'your-secret-key')

    # Socket.IO specific configuration
    app.config['SOCKETIO_ASYNC_MODE'] = 'threading'  # Use threading as async mode for better compatibility
    app.config['SOCKETIO_PING_TIMEOUT'] = 10  # Increased for more reliable connections
    app.config['SOCKETIO_PING_INTERVAL'] = 5  # Increased for more reliable connections

    # Set Flask environment
    if os.getenv('FLASK_ENV') == 'development':
        os.environ['OAUTHLIB_INSECURE_TRANSPORT'] = '1'
        app.config['PREFERRED_URL_SCHEME'] = 'http'
    else:
        app.config['PREFERRED_URL_SCHEME'] = 'https'

    # MongoDB setup
    disconnect()
    connect(
        db='kevko_systems',
        host=os.getenv('MONGO_URI'),
        alias='default'
    )
    
    # SQLAlchemy setup
    init_db(app)

    # Initialize SocketIO with app if enabled
    socketio = None
    if use_socketio:
        logging.info("Initializing Socket.IO...")
        try:
            # Use threading mode for compatibility
            socketio = SocketIO(
                app,
                cors_allowed_origins="*",
                async_mode='threading',  # Use threading for better compatibility
                logger=True,
                engineio_logger=True,
                ping_timeout=10,  # Increased for more reliable connections
                ping_interval=5,  # Increased for more reliable connections
                manage_session=False,  # Let Flask handle the sessions
                message_queue=None,  # Use in-memory queue for faster message delivery
                max_http_buffer_size=50 * 1024 * 1024,  # 50MB buffer for large messages with images
                always_connect=True  # Ensure connections are always established
            )
            logging.info("Socket.IO initialized successfully with threading mode")

            # Initialize socket handlers
            try:
                from blueprints.live.socket import init_socketio as init_live_socketio
                init_live_socketio(socketio)
                logging.info("Live Socket.IO handlers registered")
            except Exception as e:
                logging.error(f"Error initializing live socket handlers: {e}")

            # Initialize friends socket handlers
            try:
                from api.friends.socket import init_socketio as init_friends_socketio
                init_friends_socketio(socketio)
                logging.info("Friends Socket.IO handlers registered")
            except Exception as e:
                logging.error(f"Error initializing friends socket handlers: {e}")

            # Initialize presence socket handlers
            try:
                from api.presence.socket import init_presence_socketio, set_socketio_instance
                init_presence_socketio(socketio)
                set_socketio_instance(socketio)
                logging.info("Presence Socket.IO handlers registered")
            except Exception as e:
                logging.error(f"Error initializing presence socket handlers: {e}")

            # Enable Socket.IO debug mode
            socketio.server.eio.logger.setLevel('DEBUG')
            logging.info("Socket.IO debug mode enabled")

            logging.info("Socket.IO handlers registration complete")
        except Exception as e:
            logging.error(f"Error initializing Socket.IO: {e}")
            socketio = None

    # Login manager setup
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'

    @login_manager.user_loader
    def load_user(user_id):
        return User.objects(id=user_id).first()

    # Register view blueprints
    app.register_blueprint(auth_bp)       # Auth views (/auth/...)
    app.register_blueprint(chat_views)    # Chat views (/chat/...)
    app.register_blueprint(live_views)    # Live views (/live/...)
    app.register_blueprint(admin_bp)      # Admin views (/admin/...)
    app.register_blueprint(profile_page_bp) # Profile views (/profile/...)
    app.register_blueprint(spotify_bp)    # Spotify views (/spotify/...)

    # Register API blueprints
    app.register_blueprint(auth_api)          # Auth API (/api/auth/...)
    app.register_blueprint(chat_api)          # Chat API (/api/chat/...)
    app.register_blueprint(live_api)          # Live API (/api/live/...)
    app.register_blueprint(friends_api)       # Friends API (/api/friends/...)
    app.register_blueprint(profile_api)       # Profile API (/api/profile/...)
    app.register_blueprint(admin_api)         # Admin API (/api/admin/...)
    app.register_blueprint(spotify_api)       # Spotify API (/api/spotify/...)
    app.register_blueprint(user_api)          # User API (/api/user/...)
    app.register_blueprint(admin_statistics)  # Admin Statistics API (/api/admin/statistics/...)
    app.register_blueprint(model_usage_stats)  # Model Usage Statistics API (/api/admin/statistics/...)
    app.register_blueprint(engagement_api)    # Engagement API (/api/statistics/engagement/...)
    app.register_blueprint(upload_api)        # Upload API (/api/upload/...)
    app.register_blueprint(friends_theme_bp)  # Friends Theme API (/api/friends/theme...)
    # app.register_blueprint(activity_api)      # Activity API (/api/activity/...) - Removed, replaced with presence system
    app.register_blueprint(presence_api)       # Presence API (/api/presence/...)

    # Register middleware
    # No middleware currently registered
        
    # Register error handlers
    try:
        from api.error_handlers import register_error_handlers, apply_method_restrictions
        register_error_handlers(app)
        logging.info("API error handlers registered")
        
        # Apply method restrictions to all API endpoints
        apply_method_restrictions(app)
        logging.info("Method restrictions applied to all API endpoints")
    except Exception as e:
        logging.error(f"Error registering API error handlers: {str(e)}")

    # Initialize statistics scheduler
    if os.getenv('ENABLE_STATS_SCHEDULER', 'true').lower() == 'true':
        try:
            # Try to import the scheduler and stats updater
            try:
                # Try absolute import first
                from utils.scheduler import scheduler
                from utils.stats_updater import update_statistics
            except ImportError:
                # Try relative import as fallback
                import sys
                # Add the project root to the Python path
                sys.path.append(os.path.dirname(os.path.abspath(__file__)))
                from utils.scheduler import scheduler
                from utils.stats_updater import update_statistics

            # Add statistics update task (every 5 minutes)
            scheduler.add_task('update_statistics', update_statistics, 5)

            # Add presence cleanup task (every 2 minutes)
            try:
                from models.user_presence import UserPresence
                def cleanup_stale_presence():
                    UserPresence.cleanup_stale_connections(max_age_minutes=5)
                scheduler.add_task('cleanup_stale_presence', cleanup_stale_presence, 2)
                logging.info("Presence cleanup task added to scheduler")
            except Exception as e:
                logging.error(f"Error adding presence cleanup task: {str(e)}")

            scheduler.start()
            logging.info("Statistics scheduler started")
        except Exception as e:
            logging.error(f"Error initializing statistics scheduler: {str(e)}")




    # Add root route
    @app.route('/')
    def landing():
        return render_template('kevkoai-landing.html')


    @app.route('/dashboard')
    @login_required  # Assuming you want this protected like other routes
    def dashboard():
        return render_template('dashboard.html')

    return app, socketio

if __name__ == '__main__':
    # Check if Socket.IO should be enabled
    use_socketio = os.getenv('USE_SOCKETIO', 'false').lower() == 'true'
    logging.info(f"USE_SOCKETIO environment variable: {os.getenv('USE_SOCKETIO')}")
    logging.info(f"Using Socket.IO: {use_socketio}")

    # Create the Flask app with or without Socket.IO
    app, socketio = create_app(use_socketio)
    port = int(os.getenv('PORT', 2000))
    debug = os.getenv('FLASK_ENV') == 'development'

    # Print all registered routes
    print("\nAll registered routes:")
    for rule in app.url_map.iter_rules():
        print(f"  - {rule.endpoint}: {rule.rule} [{', '.join(rule.methods)}]")

    # Run the server
    if use_socketio and socketio is not None:
        try:
            # Run with SocketIO for development with live chat features
            logging.info(f"Starting server with Socket.IO enabled on port {port}")
            socketio.run(app, host='0.0.0.0', port=port, debug=debug, allow_unsafe_werkzeug=True, use_reloader=False)
        except Exception as e:
            logging.error(f"Error running Socket.IO server: {e}")
            logging.info("Falling back to regular Flask server")
            app.run(host='0.0.0.0', port=port, debug=debug)
    else:
        # Run regular Flask app for production chat functionality
        logging.info(f"Starting server without Socket.IO on port {port}")
        app.run(host='0.0.0.0', port=port, debug=debug)
